import React, { useState, useEffect, useCallback, useRef } from 'react';
import { db } from "./firebase"; // Ensure firebase is correctly configured
import { doc, getDoc, setDoc, updateDoc, collection, addDoc, getDocs, deleteDoc } from "firebase/firestore";
import { Pie } from "react-chartjs-2";
import "chart.js/auto"; // Importing Chart.js
import "./index.css";
import "./App.css";
import Login from "./Login"; // Import the Login component
import CreateAdmin from "./CreateAdmin"; // Import the CreateAdmin component
import LateGuestsDashboard from "./LateGuestsDashboard"; // Import the Late Guests Dashboard
import emailjs from '@emailjs/browser';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

// Guest masking configuration for surprise guests
const SURPRISE_GUESTS = {
  '<EMAIL>': {
    title: 'Mrs',
    name: '<PERSON><PERSON><PERSON>',
    surname: '<PERSON>',
    email: '<EMAIL>',
    phone: '+27 61 171 1529'
  },
  'kamutinidour<PERSON>@gmail.com': {
    title: 'Mrs',
    name: '<PERSON>',
    surname: 'Smith',
    email: '<EMAIL>',
    phone: '+27 81 420 7153'
  },
  '<EMAIL>': {
    title: 'Mr',
    name: 'Shane',
    surname: 'White',
    email: '<EMAIL>',
    phone: '+971 52 320 9639'
  }
};

// Function to mask guest data for surprise guests
const maskGuestData = (guest, currentUser) => {
  // Only mask for users other than onesmus and colleta
  if (!currentUser ||
      currentUser.displayName?.toLowerCase() === 'onesmus' ||
      currentUser.displayName?.toLowerCase() === 'colleta') {
    return guest; // Return original data for onesmus and colleta
  }

  // Check if this guest should be masked
  const maskedData = SURPRISE_GUESTS[guest.email];
  if (maskedData) {
    return {
      ...guest,
      title: maskedData.title,
      name: maskedData.name,
      surname: maskedData.surname,
      email: maskedData.email,
      phone: maskedData.phone
    };
  }

  return guest; // Return original data if not a surprise guest
};

// Function to generate a random password
const generatePassword = (length = 8) => {
  const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  return password;
};

const App = () => {
  const [guests, setGuests] = useState([]);
  const [filteredGuests, setFilteredGuests] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [approvalFilter, setApprovalFilter] = useState("all"); // New filter for approval status
  const [stats, setStats] = useState({
    total: 0,
    onesmus: 0,
    colleta: 0,
    approved: 0, // New stat for approved guests
    pending: 0, // New stat for pending guests
    declined: 0, // New stat for declined guests
    titles: {} // Added titles object to track Mr/Mrs/etc counts
  });
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'createAdmin', 'giftRegistry', 'tableSeating', 'emailList'
  const [statView, setStatView] = useState('behalfOf'); // 'behalfOf', 'titles', 'approval'
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [currentGuestIndex, setCurrentGuestIndex] = useState(null);
  
  // Edit states
  const [editMode, setEditMode] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [editedGuest, setEditedGuest] = useState({
    title: "",
    name: "",
    surname: "",
    email: "",
    phone: "",
    behalfOf: "",
    approvalStatus: "pending", // Default approval status
    password: "" // Added password field
  });

  // Add these state variables in the App component
  const [gifts, setGifts] = useState([]);
  const [giftClaims, setGiftClaims] = useState([]); // Track gift claims
  const [newGift, setNewGift] = useState({
    name: '',
    quantity: 1,
    description: '',
    category: 'Home',
    claimed: 0,
    images: []
  });
  // eslint-disable-next-line no-unused-vars
  const [imageFiles, setImageFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [imagePreviews, setImagePreviews] = useState([]);

  // Add these state variables for gift editing
  const [editingGift, setEditingGift] = useState(null);
  const [editGiftData, setEditGiftData] = useState({
    name: '',
    quantity: 1,
    description: '',
    category: 'Home',
    images: []
  });

  // Add this function to handle multiple image selection
  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Check if adding these files would exceed 5 images total
    if (newGift.images.length + files.length > 5) {
      alert("You can only upload up to 5 images per gift.");
      return;
    }

    const newImages = [];
    const newPreviews = [];
    let processedCount = 0;

    files.forEach((file) => {
      // Check file size (limit to 100KB to keep Firestore document size reasonable)
      if (file.size > 100 * 1024) {
        alert(`Image ${file.name} is too large. Please select images smaller than 100KB.`);
        return;
      }

      // Convert to Base64
      const reader = new FileReader();
      reader.onloadend = () => {
        newImages.push(reader.result);
        newPreviews.push(URL.createObjectURL(file));
        processedCount++;

        // When all files are processed, update state
        if (processedCount === files.length) {
          setNewGift({
            ...newGift,
            images: [...newGift.images, ...newImages]
          });
          setImagePreviews([...imagePreviews, ...newPreviews]);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  // Function to handle multiple image change during edit
  const handleEditImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Check if adding these files would exceed 5 images total
    if (editGiftData.images.length + files.length > 5) {
      alert("You can only upload up to 5 images per gift.");
      return;
    }

    const newImages = [];
    const newPreviews = [];
    let processedCount = 0;

    files.forEach((file) => {
      // Check file size (limit to 100KB to keep Firestore document size reasonable)
      if (file.size > 100 * 1024) {
        alert(`Image ${file.name} is too large. Please select images smaller than 100KB.`);
        return;
      }

      // Convert to Base64
      const reader = new FileReader();
      reader.onloadend = () => {
        newImages.push(reader.result);
        newPreviews.push(URL.createObjectURL(file));
        processedCount++;

        // When all files are processed, update state
        if (processedCount === files.length) {
          setEditGiftData({
            ...editGiftData,
            images: [...editGiftData.images, ...newImages]
          });
          setImagePreviews([...imagePreviews, ...newPreviews]);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  // Function to remove an image from new gift
  const removeNewGiftImage = (index) => {
    const updatedImages = newGift.images.filter((_, i) => i !== index);
    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);
    setNewGift({...newGift, images: updatedImages});
    setImagePreviews(updatedPreviews);
  };

  // Function to remove an image from edit gift
  const removeEditGiftImage = (index) => {
    const updatedImages = editGiftData.images.filter((_, i) => i !== index);
    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);
    setEditGiftData({...editGiftData, images: updatedImages});
    setImagePreviews(updatedPreviews);
  };

  // Memoize the updateStats function to prevent unnecessary re-renders
  const updateStats = useCallback((data) => {
    // Count titles
    const titleCounts = {};
    data.forEach(guest => {
      const title = guest.title || "Unspecified";
      titleCounts[title] = (titleCounts[title] || 0) + 1;
    });

    setStats({
      total: data.length,
      onesmus: data.filter((g) => g.behalfOf === "Onesmus").length,
      colleta: data.filter((g) => g.behalfOf === "Colleta").length,
      approved: data.filter((g) => g.approvalStatus === "approved").length,
      pending: data.filter((g) => g.approvalStatus === "pending").length,
      declined: data.filter((g) => g.approvalStatus === "declined").length,
      titles: titleCounts
    });
  }, []);

  // Memoize the fetchGuests function with useCallback
  const fetchGuests = useCallback(async () => {
    try {
      const guestListRef = doc(db, "guests", "guestList");
      const guestListDoc = await getDoc(guestListRef);

      if (guestListDoc.exists()) {
        const data = guestListDoc.data();
        let guestArray = data.guests || []; // Get the guests array
        
        // If any guests don't have approvalStatus or password, add them
        guestArray = guestArray.map(guest => ({
          ...guest,
          approvalStatus: guest.approvalStatus || "pending",
          password: guest.password || "" // Ensure all guests have a password field
        }));

        setGuests(guestArray);
        setFilteredGuests(guestArray);
        updateStats(guestArray);

        // console.log("Fetched guests:", guestArray);
      } else {
        console.error("No guest data found in Firestore.");
      }
    } catch (error) {
      console.error("Error fetching guests:", error);
    }
  }, [updateStats]);

  // Add this function to fetch gifts
  const fetchGifts = useCallback(async () => {
    try {
      const giftsCollection = collection(db, "gifts", "gifts", "items");
      const giftSnapshot = await getDocs(giftsCollection);
      const giftList = giftSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setGifts(giftList);
    } catch (error) {
      console.error("Error fetching gifts:", error);
    }
  }, []);

  // Add this function to fetch gift claims
  const fetchGiftClaims = useCallback(async () => {
    try {
      const claimersCollection = collection(db, "giftClaimers");
      const claimersSnapshot = await getDocs(claimersCollection);
      const allClaims = [];

      claimersSnapshot.docs.forEach(doc => {
        const data = doc.data();

        if (data.claimers && Array.isArray(data.claimers)) {
          // Each document contains an array of claimers
          data.claimers.forEach(claimer => {
            allClaims.push({
              id: `${doc.id}_${claimer.email}`,
              giftId: doc.id, // Use document ID as giftId
              giftName: data.giftName || claimer.giftName,
              userName: claimer.name,
              userEmail: claimer.email,
              claimedAt: claimer.claimedAt,
              quantity: 1 // Default to 1 if not specified
            });
          });
        }
      });

      setGiftClaims(allClaims);
    } catch (error) {
      console.error("Error fetching gift claims:", error);
    }
  }, []);

  // Function to get users who claimed a specific gift
  const getGiftClaimers = (giftId) => {
    return giftClaims.filter(claim => claim.giftId === giftId);
  };



  // Add this function to add a new gift
  const addGift = async (e) => {
    e.preventDefault();

    if (!newGift.name) {
      alert("Please enter a gift name");
      return;
    }

    try {
      setUploading(true);

      // Add gift to Firestore with images array
      const giftsCollection = collection(db, "gifts", "gifts", "items");
      await addDoc(giftsCollection, {
        ...newGift,
        quantity: parseInt(newGift.quantity),
        claimed: 0,
        dateAdded: new Date().toISOString()
      });

      // Reset form and refresh list
      setNewGift({
        name: '',
        quantity: 1,
        description: '',
        category: 'Home',
        claimed: 0,
        images: []
      });
      setImageFiles([]);
      setImagePreviews([]);
      fetchGifts();
    } catch (error) {
      console.error("Error adding gift:", error);
      alert("Failed to add gift");
    } finally {
      setUploading(false);
    }
  };

  // Add this function to delete a gift
  const deleteGift = async (giftId) => {
    // Only allow onesmus to delete gifts
    if (user?.displayName?.toLowerCase() !== 'onesmus') {
      alert("Only Onesmus can delete gifts.");
      return;
    }

    if (!window.confirm("Are you sure you want to delete this gift?")) return;

    try {
      // Delete the gift document from Firestore
      const giftRef = doc(db, "gifts", "gifts", "items", giftId);
      await deleteDoc(giftRef);
      fetchGifts();
    } catch (error) {
      console.error("Error deleting gift:", error);
      alert("Failed to delete gift");
    }
  };

  // Function to start editing a gift
  const startEditGift = (gift) => {
    setEditingGift(gift.id);
    setEditGiftData({
      name: gift.name,
      quantity: gift.quantity,
      description: gift.description || '',
      category: gift.category,
      images: gift.images || (gift.imageUrl ? [gift.imageUrl] : []) // Handle backward compatibility
    });
    // Set previews for existing images
    const existingImages = gift.images || (gift.imageUrl ? [gift.imageUrl] : []);
    setImagePreviews(existingImages.map(img => img));
  };

  // Function to cancel editing
  const cancelEditGift = () => {
    setEditingGift(null);
    setEditGiftData({
      name: '',
      quantity: 1,
      description: '',
      category: 'Home',
      images: []
    });
    setImagePreviews([]);
    setImageFiles([]);
  };

  // Function to save edited gift
  const saveEditGift = async () => {
    try {
      setUploading(true);

      // Update the gift in Firestore
      const giftRef = doc(db, "gifts", "gifts", "items", editingGift);
      await updateDoc(giftRef, {
        ...editGiftData,
        quantity: parseInt(editGiftData.quantity)
      });

      // Reset form and refresh list
      setEditingGift(null);
      setEditGiftData({
        name: '',
        quantity: 1,
        description: '',
        category: 'Home',
        images: []
      });
      setImagePreviews([]);
      setImageFiles([]);
      fetchGifts();
    } catch (error) {
      console.error("Error updating gift:", error);
      alert("Failed to update gift");
    } finally {
      setUploading(false);
    }
  };

  // Check if there's a logged-in user in session storage
  useEffect(() => {
    const loggedInUser = sessionStorage.getItem('user');
    if (loggedInUser) {
      setUser(JSON.parse(loggedInUser));
    }
    setLoading(false);
  }, []);

  // If user is logged in, fetch guests
  useEffect(() => {
    if (user) {
      fetchGuests();
    }
  }, [user, fetchGuests]); // Added fetchGuests to the dependency array

  // Add fetchGifts() to your existing useEffect that has fetchGuests
  const [tables, setTables] = useState([]);
  const [selectedTable, setSelectedTable] = useState(null);
  const [maxTablesCount] = useState(28);
  const [maxSeatsPerTable] = useState(10);

  // Add this function to fetch tables
  const fetchTables = useCallback(async () => {
    try {
      const tablesRef = doc(db, "tables", "seatingPlan");
      const tablesDoc = await getDoc(tablesRef);

      if (tablesDoc.exists()) {
        const existingTables = tablesDoc.data().tables || [];

        // Check if we need to add more tables
        if (existingTables.length < maxTablesCount) {
          const updatedTables = [...existingTables];

          // Add missing tables
          for (let i = existingTables.length; i < maxTablesCount; i++) {
            updatedTables.push({
              id: i + 1,
              name: `Table ${i + 1}`,
              guests: []
            });
          }

          // Update Firestore with the new tables
          await setDoc(tablesRef, { tables: updatedTables });
          setTables(updatedTables);
        } else {
          setTables(existingTables);
        }
      } else {
        // Initialize with empty tables if none exist
        const initialTables = Array(maxTablesCount).fill().map((_, i) => ({
          id: i + 1,
          name: `Table ${i + 1}`,
          guests: []
        }));
        setTables(initialTables);
        await setDoc(tablesRef, { tables: initialTables });
      }
    } catch (error) {
      console.error("Error fetching tables:", error);
    }
  }, [maxTablesCount]);

  useEffect(() => {
    if (user) {
      fetchGuests();
      fetchGifts();
      fetchGiftClaims();
      fetchTables(); // Add this line
    }
  }, [user, fetchGuests, fetchGifts, fetchGiftClaims, fetchTables]);

  const deleteGuest = async (index) => {
    // Only allow onesmus to delete guests
    if (user?.displayName?.toLowerCase() !== 'onesmus') {
      alert("Only Onesmus can delete guests.");
      return;
    }

    if (!window.confirm("Are you sure you want to delete this guest?")) return;

    try {
      // Remove the guest from local state
      const updatedGuests = [...guests];
      updatedGuests.splice(index, 1);
      setGuests(updatedGuests);
      setFilteredGuests(updatedGuests);
      updateStats(updatedGuests);

      // Update Firestore
      const guestListRef = doc(db, "guests", "guestList");
      await setDoc(guestListRef, { guests: updatedGuests }, { merge: true });

      // console.log("Guest deleted successfully");
    } catch (error) {
      console.error("Error deleting guest:", error);
    }
  };
  
  // Handle edit initiation
  const handleEditClick = (index) => {
    setEditMode(true);
    setEditIndex(index);
    // Find the actual index in the original guests array
    const originalIndex = guests.findIndex(guest => 
      guest.name === filteredGuests[index].name && 
      guest.email === filteredGuests[index].email
    );
    setEditIndex(originalIndex);
    setEditedGuest({...guests[originalIndex]});
  };

  // Add this to your state variables
  const [emailSendingStatus, setEmailSendingStatus] = useState({});

  // Add this initialization near the top of your component
  useEffect(() => {
    // Initialize EmailJS with your user ID from environment variable
    emailjs.init(process.env.REACT_APP_EMAILJS_USER_ID);
  }, []);

  // Fallback email function
  const sendEmailFallback = async (guest) => {
    try {
      const response = await fetch('https://wedding-email-system.vercel.app/api/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_FALLBACK_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to_name: `${guest.title || ""} ${guest.name} ${guest.surname}`.trim(),
          guest_email: guest.email,
          guest_password: guest.password
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('Fallback email sent successfully:', result.id);
        return true;
      } else {
        throw new Error('Fallback API returned unsuccessful response');
      }
    } catch (error) {
      console.error('Fallback email sending failed:', error);
      return false;
    }
  };

  // Handle approval status change
  const handleApprovalChange = async (index, status) => {
    try {
      // Find the actual index in the original guests array
      const originalIndex = guests.findIndex(guest => 
        guest.name === filteredGuests[index].name && 
        guest.email === filteredGuests[index].email
      );
      
      if (originalIndex === -1) {
        console.error("Guest not found in original array");
        return;
      }
      
      // Generate password only if changing to "approved" and no password exists
      let password = "";
      if (status === "approved" && !guests[originalIndex].password) {
        password = generatePassword();
        setCurrentPassword(password);
        setCurrentGuestIndex(originalIndex);
        setShowPasswordModal(true);
      }
      
      // Update guest in local state
      const updatedGuests = [...guests];
      updatedGuests[originalIndex] = {
        ...updatedGuests[originalIndex],
        approvalStatus: status,
        password: status === "approved" ? (password || updatedGuests[originalIndex].password || generatePassword()) : "",
      };
      
      // Update Firestore first to ensure database consistency
      const guestListRef = doc(db, "guests", "guestList");
      await setDoc(guestListRef, { guests: updatedGuests }, { merge: true });
      console.log(`Guest approval status updated to ${status} in Firestore`);
      
      // Then update local state
      setGuests(updatedGuests);
      
      // Apply current filters
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      // Update stats
      updateStats(updatedGuests);
      
      // If approving, update email sending status
      if (status === "approved") {
        setEmailSendingStatus(prev => ({
          ...prev,
          [updatedGuests[originalIndex].email]: "sending"
        }));
        
        const guest = updatedGuests[originalIndex];
        
        // Enhanced email sending function with fallback
        const sendApprovalEmail = async (guest) => {
          // First try EmailJS
          try {
            const response = await emailjs.send(
              process.env.REACT_APP_EMAILJS_SERVICE_ID,
              process.env.REACT_APP_EMAILJS_TEMPLATE_ID,
              {
                to_name: `${guest.title || ""} ${guest.name} ${guest.surname}`.trim(),
                guest_email: guest.email,
                guest_password: guest.password,
                reply_to: "<EMAIL>"
              }
            );

            // Check if the response status is 200 (OK)
            if (response.status === 200) {
              console.log('EmailJS email sent successfully');
              return true;
            } else {
              throw new Error(`EmailJS failed with status: ${response.status}`);
            }
          } catch (emailjsError) {
            console.warn('EmailJS failed, trying fallback API:', emailjsError);

            // Try fallback API
            const fallbackSuccess = await sendEmailFallback(guest);

            if (fallbackSuccess) {
              return true;
            } else {
              console.error('Both EmailJS and fallback API failed');
              return false;
            }
          }
        };

        // Send email with fallback support
        const emailSuccess = await sendApprovalEmail(guest);

        if (emailSuccess) {
          // Update email status to sent
          setEmailSendingStatus(prev => ({
            ...prev,
            [guest.email]: "sent"
          }));

          // Update the guest's emailSent status
          const emailSentGuests = [...updatedGuests]; // Use the already updated guests array
          const guestIndex = emailSentGuests.findIndex(g => g.email === guest.email);
          if (guestIndex !== -1) {
            emailSentGuests[guestIndex] = {
              ...emailSentGuests[guestIndex],
              emailSent: true
            };

            // Update Firestore with email sent status
            await setDoc(guestListRef, { guests: emailSentGuests }, { merge: true });

            // Update local state
            setGuests(emailSentGuests);
          }
        } else {
          // Update email status to error if both methods failed
          setEmailSendingStatus(prev => ({
            ...prev,
            [guest.email]: "error"
          }));
        }
      }
      
    } catch (error) {
      console.error(`Error ${status} guest:`, error);
      alert(`Failed to update guest status to ${status}. Please try again.`);
    }
  };

  // Close password modal and continue
  const closePasswordModal = async () => {
    setShowPasswordModal(false);
    
    // Ensure the password is saved to the guest
    if (currentGuestIndex !== null) {
      const updatedGuests = [...guests];
      updatedGuests[currentGuestIndex] = {
        ...updatedGuests[currentGuestIndex],
        password: currentPassword
      };
      
      setGuests(updatedGuests);
      
      // Apply current filters
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      // Update Firestore
      const guestListRef = doc(db, "guests", "guestList");
      await setDoc(guestListRef, { guests: updatedGuests }, { merge: true });
      
      // Reset current guest
      setCurrentGuestIndex(null);
    }
  };

  // Copy password to clipboard
  const copyPasswordToClipboard = () => {
    navigator.clipboard.writeText(currentPassword)
      .then(() => {
        alert("Password copied to clipboard!");
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
      });
  };

  // Generate new password
  const regeneratePassword = () => {
    const newPassword = generatePassword();
    setCurrentPassword(newPassword);
  };

  // Handle input changes in edit form
  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditedGuest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save edited guest
  const saveEditedGuest = async () => {
    try {
      // Update guest in local state
      const updatedGuests = [...guests];
      updatedGuests[editIndex] = editedGuest;
      
      setGuests(updatedGuests);
      
      // Apply current filters
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      // Update stats
      updateStats(updatedGuests);
      
      // Update Firestore
      const guestListRef = doc(db, "guests", "guestList");
      await setDoc(guestListRef, { guests: updatedGuests }, { merge: true });
      
      // Reset edit mode
      setEditMode(false);
      setEditIndex(null);
      setEditedGuest({
        title: "",
        name: "",
        surname: "",
        email: "",
        phone: "",
        behalfOf: "",
        approvalStatus: "pending",
        password: ""
      });
      
      // console.log("Guest updated successfully");
    } catch (error) {
      console.error("Error updating guest:", error);
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditMode(false);
    setEditIndex(null);
    setEditedGuest({
      title: "",
      name: "",
      surname: "",
      email: "",
      phone: "",
      behalfOf: "",
      approvalStatus: "pending",
      password: ""
    });
  };

  const handleSearch = (event) => {
    const term = event.target.value.toLowerCase();
    setSearchTerm(term);
    applyFilters(term, filterBy, approvalFilter);
  };

  // Add this function to handle filter changes for "Behalf Of"
  // eslint-disable-next-line no-unused-vars
  const handleBehalfOfChange = (event) => {
    const value = event.target.value;
    setFilterBy(value);
    applyFilters(searchTerm, value, approvalFilter);
  };

  const handleFilterChange = (event) => {
    const value = event.target.value;
    setFilterBy(value);
    applyFilters(searchTerm, value, approvalFilter);
  };

  const handleApprovalFilterChange = (event) => {
    const value = event.target.value;
    setApprovalFilter(value);
    applyFilters(searchTerm, filterBy, value);
  };

  // Toggle between stats views
  const handleStatViewChange = (view) => {
    setStatView(view);
  };

  // Combined filter function
  const applyFilters = useCallback((term, filter, approvalFilter) => {
    let filtered = guests.filter((guest) => {
      const matchesSearch =
        guest.name.toLowerCase() === term ||
        guest.surname.toLowerCase() === term ||
        guest.email.toLowerCase().includes(term) ||
        guest.phone.includes(term);

      // Make sure we're comparing the same case for behalfOf
      const matchesBehalfOf = filter === "all" ||
        guest.behalfOf === filter; // Removed toLowerCase() to match exact values

      const matchesApproval = approvalFilter === "all" ||
        guest.approvalStatus === approvalFilter;

      return matchesSearch && matchesBehalfOf && matchesApproval;
    });

    setFilteredGuests(filtered);
  }, [guests]);

  const exportToCSV = () => {
    const headers = ["Title","Name", "Surname", "Email", "Phone", "Behalf Of", "Approval Status", "Password"];
    const csvData = filteredGuests.map((guest) =>
      [guest.title, guest.name, guest.surname, guest.email, guest.phone, guest.behalfOf, guest.approvalStatus, guest.password].join(",")
    );

    const csv = [headers.join(","), ...csvData].join("\n");
    const blob = new Blob([csv], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "wedding_guests.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleLogin = async (userData) => {
    // Update lastLogin in Firestore
    try {
      const userRef = doc(db, "user", "user");
      await updateDoc(userRef, {
        lastLogin: userData.lastLogin
      });
    } catch (error) {
      console.error("Error updating last login:", error);
    }

    // Save user to session storage to persist login
    sessionStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);
  };

  const handleLogout = () => {
    sessionStorage.removeItem('user');
    setUser(null);
    setCurrentView('dashboard');
  };

  // Calculate colors for charts
  const getChartColors = (type) => {
    // Base colors
    if (type === 'behalfOf') {
      return {
        backgroundColors: ["#6D8B74", "#D9CAB3"],
        hoverColors: ["#5A7561", "#B8A992"]
      };
    } else if (type === 'approval') {
      return {
        backgroundColors: ["#4CAF50", "#FFC107", "#F44336"],
        hoverColors: ["#43A047", "#FFB300", "#E53935"]
      };
    } else {
      // For titles
      const baseColors = [
        '#6D8B74', '#D9CAB3', '#5F7161', '#EFEAD8', 
        '#4B644A', '#C2B092', '#3C4F3A', '#A29171'
      ];
      
      const backgroundColors = [];
      const hoverColors = [];
      const titles = Object.keys(stats.titles);
      
      titles.forEach((_, index) => {
        const colorIndex = index % baseColors.length;
        backgroundColors.push(baseColors[colorIndex]);
        // Slightly darker for hover
        hoverColors.push(baseColors[colorIndex].replace(/[0-9a-f]{2}$/i, '90'));
      });
      
      return { backgroundColors, hoverColors };
    }
  };

  // Navigation handler
  const handleNavigation = (view) => {
    setCurrentView(view);
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  // If no user is logged in, show login screen
  if (!user) {
    return <Login onLogin={handleLogin} />;
  }

  // Password modal component
  const PasswordModal = () => (
    <div className="password-modal-overlay">
      <div className="password-modal">
        <h2>Guest Password Generated</h2>
        <p>A password has been generated for {guests[currentGuestIndex]?.name} {guests[currentGuestIndex]?.surname}:</p>
        
        <div className="password-display">
          <input type="text" value={currentPassword} readOnly />
          <button onClick={copyPasswordToClipboard} className="copy-button">Copy</button>
        </div>
        
        <p>You can provide this password to the guest for their access.</p>
        
        <div className="password-modal-actions">
          <button onClick={regeneratePassword} className="regenerate-button">Generate New Password</button>
          <button onClick={closePasswordModal} className="confirm-button">Confirm</button>
        </div>
      </div>
    </div>
  );

  // Edit form component
  const EditForm = () => (
    <div className="edit-form-overlay">
      <div className="edit-form">
        <h2>Edit Guest</h2>
        <div className="form-group">
          <label>Title:</label>
          <input
            type="text"
            name="title"
            value={editedGuest.title}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Name:</label>
          <input
            type="text"
            name="name"
            value={editedGuest.name}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Surname:</label>
          <input
            type="text"
            name="surname"
            value={editedGuest.surname}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Email:</label>
          <input
            type="email"
            name="email"
            value={editedGuest.email}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Phone:</label>
          <input
            type="text"
            name="phone"
            value={editedGuest.phone}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Behalf Of:</label>
          <select
            name="behalfOf"
            value={editedGuest.behalfOf}
            onChange={handleEditInputChange}
          >
            <option value="Onesmus">Onesmus</option>
            <option value="Colleta">Colleta</option>
          </select>
        </div>
        <div className="form-group">
          <label>Approval Status:</label>
          <select
            name="approvalStatus"
            value={editedGuest.approvalStatus}
            onChange={handleEditInputChange}
          >
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="declined">Declined</option>
          </select>
        </div>
        {editedGuest.approvalStatus === "approved" && (
          <div className="form-group">
            <label>Password:</label>
            <div className="password-input-group">
              <input
                type="text"
                name="password"
                value={editedGuest.password}
                onChange={handleEditInputChange}
              />
              <button 
                onClick={() => {
                  const newPassword = generatePassword();
                  setEditedGuest(prev => ({...prev, password: newPassword}));
                }}
                type="button"
              >
                Generate
              </button>
            </div>
          </div>
        )}
        <div className="form-buttons">
          <button onClick={saveEditedGuest}>Save</button>
          <button onClick={cancelEdit} className="cancel-button">Cancel</button>
        </div>
      </div>
    </div>
  );

  // Titles stats component
  const TitlesStatsView = () => {
    const titles = Object.entries(stats.titles).sort((a, b) => b[1] - a[1]);
    const { backgroundColors, hoverColors } = getChartColors('titles');
    
    return (
      <>
        <div className="stats-container">
          {titles.slice(0, 3).map(([title, count], index) => (
            <div className="stats-card" key={title}>
              <p>{title} Guests</p>
              <span>{count}</span>
            </div>
          ))}
        </div>
        
        <div className="chart-container">
          <Pie
            data={{
              labels: titles.map(([title]) => `${title} (${title === 'Unspecified' ? 'No Title' : title})`),
              datasets: [
                {
                  data: titles.map(([_, count]) => count),
                  backgroundColor: backgroundColors,
                  hoverBackgroundColor: hoverColors,
                },
              ],
            }}
          />
        </div>
      </>
    );
  };

  // Default stats view
  const BehalfOfStatsView = () => {
    const { backgroundColors, hoverColors } = getChartColors('behalfOf');
    
    return (
      <>
        <div className="stats-container">
          <div className="stats-card">
            <p>Onesmus's Guests</p>
            <span>{stats.onesmus}</span>
          </div>
          <div className="stats-card">
            <p>Total Guests</p>
            <span>{stats.total}</span>
          </div>
          <div className="stats-card">
            <p>Colleta's Guests</p>
            <span>{stats.colleta}</span>
          </div>
        </div>

        <div className="chart-container">
          <Pie
            data={{
              labels: ["Onesmus' Guests", "Colleta's Guests"],
              datasets: [
                {
                  data: [stats.onesmus, stats.colleta],
                  backgroundColor: backgroundColors,
                  hoverBackgroundColor: hoverColors,
                },
              ],
            }}
          />
        </div>
      </>
    );
  };

  // Approval stats view
  const ApprovalStatsView = () => {
    const { backgroundColors, hoverColors } = getChartColors('approval');
    
    return (
      <>
        <div className="stats-container">
          <div className="stats-card approval-pending">
            <p>Pending Approval</p>
            <span>{stats.pending}</span>
          </div>
          <div className="stats-card approval-approved">
            <p>Approved</p>
            <span>{stats.approved}</span>
          </div>
          <div className="stats-card approval-declined">
            <p>Declined</p>
            <span>{stats.declined}</span>
          </div>
        </div>

        <div className="chart-container">
          <Pie
            data={{
              labels: ["Approved", "Pending", "Declined"],
              datasets: [
                {
                  data: [stats.approved, stats.pending, stats.declined],
                  backgroundColor: backgroundColors,
                  hoverBackgroundColor: hoverColors,
                },
              ],
            }}
          />
        </div>
      </>
    );
  };

  // Add this component inside your App component before the return statement
  // Make sure it's defined before it's used in the JSX
  const GiftRegistryView = () => {
    const nameInputRef = useRef(null);

    useEffect(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
      }
    }, []); // Focus on mount

    return (
      <div className="container">
        <h1>Gift Registry Management</h1>
        
        {editingGift ? (
          <div className="gift-form-container">
            <h2>Edit Gift</h2>
            <form onSubmit={(e) => { e.preventDefault(); saveEditGift(); }} className="gift-form">
              <div className="form-group">
                <label htmlFor="editGiftName">Gift Name</label>
                <input 
                  id="editGiftName"
                  type="text" 
                  value={editGiftData.name} 
                  onChange={(e) => setEditGiftData({...editGiftData, name: e.target.value})}
                  required
                  autoFocus
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="editGiftQuantity">Quantity</label>
                <input 
                  id="editGiftQuantity"
                  type="number" 
                  min="1"
                  value={editGiftData.quantity} 
                  onChange={(e) => setEditGiftData({...editGiftData, quantity: e.target.value})}
                  required
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="editGiftCategory">Category</label>
                <select 
                  id="editGiftCategory"
                  value={editGiftData.category} 
                  onChange={(e) => setEditGiftData({...editGiftData, category: e.target.value})}
                >
                  <option value="Home">Home</option>
                  <option value="Kitchen">Kitchen</option>
                  <option value="Bedroom">Bedroom</option>
                  <option value="Bathroom">Bathroom</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="editGiftImage">Gift Images (up to 5)</label>
                <input
                  id="editGiftImage"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleEditImageChange}
                />
                {editGiftData.images.length > 0 && (
                  <div className="images-preview">
                    {editGiftData.images.map((image, index) => (
                      <div key={index} className="image-preview-item">
                        <img
                          src={image}
                          alt={`Preview ${index + 1}`}
                          style={{ width: '100px', height: '100px', objectFit: 'cover' }}
                        />
                        <button
                          type="button"
                          onClick={() => removeEditGiftImage(index)}
                          className="remove-image-button"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="form-group">
                <label htmlFor="editGiftDescription">Description</label>
                <textarea 
                  id="editGiftDescription"
                  value={editGiftData.description} 
                  onChange={(e) => setEditGiftData({...editGiftData, description: e.target.value})}
                />
              </div>
              
              <div className="form-buttons">
                <button 
                  type="submit" 
                  className="save-gift-button" 
                  disabled={uploading}
                >
                  {uploading ? 'Saving...' : 'Save Changes'}
                </button>
                <button 
                  type="button" 
                  className="cancel-button" 
                  onClick={cancelEditGift}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        ) : (
          <div className="gift-form-container">
            <h2>Add New Gift</h2>
            <form onSubmit={addGift} className="gift-form">
              <div className="form-group">
                <label htmlFor="giftName">Gift Name</label>
                <input 
                  id="giftName"
                  type="text" 
                  ref={nameInputRef}
                  value={newGift.name} 
                  onChange={(e) => setNewGift({...newGift, name: e.target.value})}
                  required
                  autoFocus
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="giftQuantity">Quantity</label>
                <input 
                  id="giftQuantity"
                  type="number" 
                  min="1"
                  value={newGift.quantity} 
                  onChange={(e) => setNewGift({...newGift, quantity: e.target.value})}
                  required
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="giftCategory">Category</label>
                <select 
                  id="giftCategory"
                  value={newGift.category} 
                  onChange={(e) => setNewGift({...newGift, category: e.target.value})}
                >
                  <option value="Home">Home</option>
                  <option value="Kitchen">Kitchen</option>
                  <option value="Bedroom">Bedroom</option>
                  <option value="Bathroom">Bathroom</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="giftImage">Gift Images (up to 5)</label>
                <input
                  id="giftImage"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageChange}
                />
                {newGift.images.length > 0 && (
                  <div className="images-preview">
                    {newGift.images.map((image, index) => (
                      <div key={index} className="image-preview-item" style={{ position: 'relative', display: 'inline-block' }}>
                        <img
                          src={image}
                          alt={`Preview ${index + 1}`}
                          style={{ width: '100px', height: '100px', objectFit: 'cover', margin: '5px' }}
                        />
                        <button
                          type="button"
                          onClick={() => removeNewGiftImage(index)}
                          className="remove-image-button"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="form-group">
                <label htmlFor="giftDescription">Description</label>
                <textarea 
                  id="giftDescription"
                  value={newGift.description} 
                  onChange={(e) => setNewGift({...newGift, description: e.target.value})}
                />
              </div>
              
              <button 
                type="submit" 
                className="add-gift-button" 
                disabled={uploading}
              >
                {uploading ? 'Uploading...' : 'Add Gift'}
              </button>
            </form>
          </div>
        )}
        
        <div className="gift-list-container">
          <h2>Gift Registry</h2>
          {gifts.length === 0 ? (
            <p>No gifts added yet.</p>
          ) : (
            <table>
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Gift Name</th>
                  <th>Category</th>
                  <th>Quantity</th>
                  <th>Claimed</th>
                  <th>Claimed By</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {gifts.map((gift) => (
                  <tr key={gift.id}>
                    <td>
                      {(gift.images && gift.images.length > 0) || gift.imageUrl ? (
                        <div className="gift-images-display">
                          {gift.images && gift.images.length > 0 ? (
                            // Display multiple images
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2px' }}>
                              {gift.images.slice(0, 3).map((image, index) => (
                                <img
                                  key={index}
                                  src={image}
                                  alt={`${gift.name} ${index + 1}`}
                                  style={{ width: '30px', height: '30px', objectFit: 'cover' }}
                                />
                              ))}
                              {gift.images.length > 3 && (
                                <div style={{
                                  width: '30px',
                                  height: '30px',
                                  backgroundColor: '#f0f0f0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '10px'
                                }}>
                                  +{gift.images.length - 3}
                                </div>
                              )}
                            </div>
                          ) : (
                            // Backward compatibility for single imageUrl
                            <img
                              src={gift.imageUrl}
                              alt={gift.name}
                              style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                            />
                          )}
                        </div>
                      ) : (
                        <div className="no-image">No Image</div>
                      )}
                    </td>
                    <td>{gift.name}</td>
                    <td>{gift.category}</td>
                    <td>{gift.quantity}</td>
                    <td>{gift.claimed || 0}</td>
                    <td className="gift-claims-cell">
                      {(() => {
                        const claimers = getGiftClaimers(gift.id);
                        if (claimers.length === 0) {
                          return <span className="no-claims">No claims yet</span>;
                        }
                        return (
                          <div>
                            {claimers.map((claim, index) => (
                              <div key={index} className="gift-claim-item">
                                <div className="gift-claim-user">
                                  {claim.userName || claim.userEmail}
                                  {claim.quantity > 1 && <span className="gift-claim-quantity"> (×{claim.quantity})</span>}
                                </div>
                                <div className="gift-claim-date">
                                  {claim.claimedAt ? new Date(claim.claimedAt).toLocaleDateString() : 'Date unknown'}
                                </div>
                              </div>
                            ))}
                          </div>
                        );
                      })()}
                    </td>
                    <td>{gift.description}</td>
                    <td className="action-buttons">
                      <button
                        onClick={() => startEditGift(gift)}
                        className="edit-button"
                        title="Edit"
                      >
                        ✎
                      </button>
                      {user?.displayName?.toLowerCase() === 'onesmus' && (
                        <button
                          onClick={() => deleteGift(gift.id)}
                          className="delete-button"
                          title="Delete"
                        >
                          🗑️
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    );
  };

  // Helper function to create a unique guest identifier
  const getGuestId = (guest) => {
    return `${guest.email}-${guest.name}-${guest.surname}`;
  };

  // Function to remove a guest from a table
  const removeGuestFromTable = async (tableId, guestId) => {
    // Only allow onesmus to remove guests from tables
    if (user?.displayName?.toLowerCase() !== 'onesmus') {
      alert("Only Onesmus can remove guests from tables.");
      return;
    }

    try {
      const updatedTables = tables.map(table => {
        if (table.id === tableId) {
          return {
            ...table,
            guests: table.guests.filter(g => getGuestId(g) !== guestId)
          };
        }
        return table;
      });

      setTables(updatedTables);
      const tablesRef = doc(db, "tables", "seatingPlan");
      await setDoc(tablesRef, { tables: updatedTables });
    } catch (error) {
      console.error("Error removing guest from table:", error);
    }
  };

  // Add this component for the Email List view
  const EmailListView = () => {
    const [lateGuests, setLateGuests] = useState([]);

    // Fetch late guests
    const fetchLateGuests = useCallback(async () => {
      try {
        const lateListRef = doc(db, "guests", "late_list");
        const lateListDoc = await getDoc(lateListRef);

        if (lateListDoc.exists()) {
          const data = lateListDoc.data();
          let guestArray = data.guests || [];

          guestArray = guestArray.map(guest => ({
            ...guest,
            approvalStatus: guest.approvalStatus || "pending",
            password: guest.password || "",
            isLateGuest: true // Add flag to identify late guests
          }));

          setLateGuests(guestArray);
        }
      } catch (error) {
        console.error("Error fetching late guests:", error);
      }
    }, []);

    useEffect(() => {
      if (user) {
        fetchLateGuests();
      }
    }, [fetchLateGuests]);

    // Combine main guests and late guests
    const allGuests = [...guests, ...lateGuests];

    // Get unique emails from all guests (main + late)
    const uniqueEmails = [...new Set(allGuests.map(guest => guest.email))].filter(email => email && email.trim() !== '');

    // Get guest details for each unique email
    const emailData = uniqueEmails.map(email => {
      const guest = allGuests.find(g => g.email === email);
      const maskedGuest = guest ? maskGuestData(guest, user) : null;
      return {
        email: maskedGuest ? maskedGuest.email : email,
        name: maskedGuest ? `${maskedGuest.title || ''} ${maskedGuest.name} ${maskedGuest.surname}`.trim() : 'Unknown',
        approvalStatus: guest ? guest.approvalStatus : 'unknown',
        behalfOf: guest ? guest.behalfOf : 'unknown',
        isLateGuest: guest ? guest.isLateGuest : false
      };
    });

    // Sort emails alphabetically
    const sortedEmailData = emailData.sort((a, b) => a.email.localeCompare(b.email));

    // Function to copy all emails to clipboard
    const copyAllEmails = () => {
      const emailList = uniqueEmails.join(', ');
      navigator.clipboard.writeText(emailList)
        .then(() => {
          alert(`${uniqueEmails.length} emails copied to clipboard!`);
        })
        .catch(err => {
          console.error('Could not copy emails: ', err);
          alert('Failed to copy emails to clipboard');
        });
    };

    // Function to copy approved emails only
    const copyApprovedEmails = () => {
      const approvedEmails = sortedEmailData
        .filter(data => data.approvalStatus === 'approved')
        .map(data => data.email);

      if (approvedEmails.length === 0) {
        alert('No approved guests found');
        return;
      }

      const emailList = approvedEmails.join(', ');
      navigator.clipboard.writeText(emailList)
        .then(() => {
          alert(`${approvedEmails.length} approved emails copied to clipboard!`);
        })
        .catch(err => {
          console.error('Could not copy emails: ', err);
          alert('Failed to copy emails to clipboard');
        });
    };

    // Function to copy approved emails formatted for Gmail BCC
    const copyApprovedEmailsForBCC = () => {
      const approvedEmails = sortedEmailData
        .filter(data => data.approvalStatus === 'approved')
        .map(data => data.email);

      if (approvedEmails.length === 0) {
        alert('No approved guests found');
        return;
      }

      // Format for Gmail BCC (semicolon separated works better than comma)
      const emailList = approvedEmails.join('; ');
      navigator.clipboard.writeText(emailList)
        .then(() => {
          alert(`${approvedEmails.length} approved emails copied for BCC! Paste into Gmail's BCC field.`);
        })
        .catch(err => {
          console.error('Could not copy emails: ', err);
          alert('Failed to copy emails to clipboard');
        });
    };

    // Function to export emails to CSV
    const exportEmailsToCSV = () => {
      const headers = ["Email", "Name", "Approval Status", "Behalf Of"];
      const csvData = sortedEmailData.map((data) =>
        [data.email, data.name, data.approvalStatus, data.behalfOf].join(",")
      );

      const csv = [headers.join(","), ...csvData].join("\n");
      const blob = new Blob([csv], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "wedding_guest_emails.csv";
      a.click();
      window.URL.revokeObjectURL(url);
    };

    return (
      <div className="container">
        <h1>Guest Email List (Including Late Guests)</h1>

        <div className="email-stats">
          <div className="stats-card">
            <p>Total Unique Emails</p>
            <span>{uniqueEmails.length}</span>
          </div>
          <div className="stats-card">
            <p>Approved Guests</p>
            <span>{sortedEmailData.filter(data => data.approvalStatus === 'approved').length}</span>
          </div>
          <div className="stats-card">
            <p>Pending Guests</p>
            <span>{sortedEmailData.filter(data => data.approvalStatus === 'pending').length}</span>
          </div>
          <div className="stats-card">
            <p>Late Guests</p>
            <span>{sortedEmailData.filter(data => data.isLateGuest).length}</span>
          </div>
        </div>

        <div className="email-actions">
          <button onClick={copyAllEmails} className="copy-button">
            Copy All Emails
          </button>
          <button onClick={copyApprovedEmails} className="copy-button approved">
            Copy Approved Emails
          </button>
          <button onClick={copyApprovedEmailsForBCC} className="copy-button bcc">
            Copy for Gmail BCC
          </button>
          <button onClick={exportEmailsToCSV} className="export-button">
            Export to CSV
          </button>
        </div>

        <div className="email-list-container">
          <h2>Email Addresses ({uniqueEmails.length} unique)</h2>
          {uniqueEmails.length === 0 ? (
            <p>No email addresses found.</p>
          ) : (
            <table className="email-table">
              <thead>
                <tr>
                  <th>Email</th>
                  <th>Guest Name</th>
                  <th>Status</th>
                  <th>Behalf Of</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedEmailData.map((data, index) => (
                  <tr key={index} className={`status-${data.approvalStatus}`}>
                    <td className="email-cell">
                      <a href={`mailto:${data.email}`} className="email-link">
                        {data.email}
                      </a>
                    </td>
                    <td>
                      {data.name}
                      {data.isLateGuest && (
                        <span className="late-guest-badge" title="Late Guest">
                          🕐
                        </span>
                      )}
                    </td>
                    <td className={`status-cell ${data.approvalStatus}`}>
                      {data.approvalStatus.charAt(0).toUpperCase() + data.approvalStatus.slice(1)}
                    </td>
                    <td>{data.behalfOf}</td>
                    <td>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(data.email)
                            .then(() => alert('Email copied to clipboard!'))
                            .catch(() => alert('Failed to copy email'));
                        }}
                        className="copy-single-button"
                        title="Copy this email"
                      >
                        📋
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    );
  };

  // Add this component for the Table Seating view
  const TableSeatingView = () => {
    const [availableGuests, setAvailableGuests] = useState([]);
    const [lateGuests, setLateGuests] = useState([]);
    const [guestSearch, setGuestSearch] = useState('');

    // Fetch late guests
    const fetchLateGuests = useCallback(async () => {
      try {
        const lateListRef = doc(db, "guests", "late_list");
        const lateListDoc = await getDoc(lateListRef);

        if (lateListDoc.exists()) {
          const data = lateListDoc.data();
          let guestArray = data.guests || [];

          guestArray = guestArray.map(guest => ({
            ...guest,
            approvalStatus: guest.approvalStatus || "pending",
            password: guest.password || "",
            isLateGuest: true // Add flag to identify late guests
          }));

          setLateGuests(guestArray);
        }
      } catch (error) {
        console.error("Error fetching late guests:", error);
      }
    }, []);

    useEffect(() => {
      if (user) {
        fetchLateGuests();
      }
    }, [fetchLateGuests]);

    useEffect(() => {
      // Combine main guests and late guests, only approved ones
      const approvedMainGuests = guests.filter(guest => guest.approvalStatus === "approved");
      const approvedLateGuests = lateGuests.filter(guest => guest.approvalStatus === "approved");

      // Combine both arrays - keep all guests, including those with duplicate emails
      const combinedGuests = [...approvedMainGuests, ...approvedLateGuests];

      setAvailableGuests(combinedGuests);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [guests, lateGuests]); // Update when either guest list changes
    
    // Filter guests based on search
    const filteredAvailableGuests = availableGuests.filter(guest => {
      if (!guestSearch || guestSearch.trim() === '') {
        return true; // Show all guests if no search term
      }

      const searchTerm = guestSearch.toLowerCase().trim();
      const name = (guest.name || '').toLowerCase().trim();
      const surname = (guest.surname || '').toLowerCase().trim();

      // Search for names/surnames that start with the search term
      const nameMatch = name.startsWith(searchTerm);
      const surnameMatch = surname.startsWith(searchTerm);

      return nameMatch || surnameMatch;
    });

    // Function to assign a guest to a table (moved inside component to access availableGuests)
    const assignGuestToTable = async (guestId, tableId) => {
      try {
        // Find the guest by unique ID in the available guests (which includes both main and late guests)
        const guest = availableGuests.find(g => getGuestId(g) === guestId);
        if (!guest) {
          console.error("Guest not found:", guestId);
          return;
        }

        // Check if table has space
        const targetTable = tables.find(table => table.id === tableId);
        if (targetTable.guests.length >= maxSeatsPerTable) {
          alert(`Table ${targetTable.name} is full (max ${maxSeatsPerTable} guests)`);
          return;
        }

        // Remove guest from any existing table using the unique guest ID
        const updatedTables = tables.map(table => ({
          ...table,
          guests: table.guests.filter(g => getGuestId(g) !== guestId)
        }));

        // Add guest to the selected table
        const tableIndex = updatedTables.findIndex(t => t.id === tableId);
        updatedTables[tableIndex].guests.push({
          name: guest.name,
          email: guest.email,
          title: guest.title || '',
          surname: guest.surname || '',
          isLateGuest: guest.isLateGuest || false
        });

        // Update state and Firestore
        setTables(updatedTables);
        const tablesRef = doc(db, "tables", "seatingPlan");
        await setDoc(tablesRef, { tables: updatedTables });
      } catch (error) {
        console.error("Error assigning guest to table:", error);
      }
    };

    // Function to export table seating arrangement to PDF
    const exportToPDF = () => {
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(20);
      doc.text('Wedding Table Seating Arrangement', 20, 20);

      // Add date
      doc.setFontSize(12);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 35);

      let yPosition = 50;

      // Filter tables that have guests
      const tablesWithGuests = tables.filter(table => table.guests.length > 0);

      if (tablesWithGuests.length === 0) {
        doc.setFontSize(14);
        doc.text('No guests have been assigned to tables yet.', 20, yPosition);
      } else {
        tablesWithGuests.forEach((table) => {
          // Check if we need a new page
          if (yPosition > 250) {
            doc.addPage();
            yPosition = 20;
          }

          // Table header
          doc.setFontSize(14);
          doc.setFont(undefined, 'bold');
          doc.text(`${table.name} (${table.guests.length}/${maxSeatsPerTable} guests)`, 20, yPosition);
          yPosition += 10;

          // Guest list for this table
          doc.setFontSize(10);
          doc.setFont(undefined, 'normal');

          const tableData = table.guests.map((guest, guestIndex) => [
            guestIndex + 1,
            guest.title || '',
            guest.name || '',
            guest.surname || '',
            guest.isLateGuest ? 'Late Guest' : 'Regular Guest'
          ]);

          autoTable(doc, {
            startY: yPosition,
            head: [['#', 'Title', 'Name', 'Surname', 'Type']],
            body: tableData,
            margin: { left: 20 },
            styles: { fontSize: 9 },
            headStyles: { fillColor: [109, 139, 116] },
            columnStyles: {
              0: { cellWidth: 15 },
              1: { cellWidth: 20 },
              2: { cellWidth: 40 },
              3: { cellWidth: 40 },
              4: { cellWidth: 35 }
            }
          });

          yPosition = doc.lastAutoTable.finalY + 15;
        });

        // Add summary at the end
        if (yPosition > 250) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('Summary:', 20, yPosition);
        yPosition += 10;

        doc.setFont(undefined, 'normal');
        const totalAssignedGuests = tables.reduce((sum, table) => sum + table.guests.length, 0);
        doc.text(`Total Tables: ${tables.length}`, 20, yPosition);
        yPosition += 7;
        doc.text(`Tables with Guests: ${tablesWithGuests.length}`, 20, yPosition);
        yPosition += 7;
        doc.text(`Total Assigned Guests: ${totalAssignedGuests}`, 20, yPosition);
        yPosition += 7;
        doc.text(`Available Seats: ${tables.length * maxSeatsPerTable}`, 20, yPosition);
      }

      doc.save('wedding-table-seating-arrangement.pdf');
    };

    // Function to export table seating arrangement to Excel
    const exportToExcel = () => {
      const workbook = XLSX.utils.book_new();

      // Check if there are any assigned guests
      const totalAssignedGuests = tables.reduce((sum, table) => sum + table.guests.length, 0);
      if (totalAssignedGuests === 0) {
        alert('No guests have been assigned to tables yet.');
        return;
      }

      // Create summary sheet
      const summaryData = [
        ['Wedding Table Seating Arrangement'],
        [`Generated on: ${new Date().toLocaleDateString()}`],
        [''],
        ['Summary:'],
        ['Total Tables', tables.length],
        ['Tables with Guests', tables.filter(table => table.guests.length > 0).length],
        ['Total Assigned Guests', tables.reduce((sum, table) => sum + table.guests.length, 0)],
        ['Available Seats', tables.length * maxSeatsPerTable],
        [''],
        ['Table Details:']
      ];

      // Add table details to summary
      tables.forEach(table => {
        if (table.guests.length > 0) {
          summaryData.push([table.name, `${table.guests.length}/${maxSeatsPerTable} guests`]);
        }
      });

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

      // Create detailed seating sheet
      const detailedData = [
        ['Table', 'Seat #', 'Title', 'Name', 'Surname', 'Email', 'Type']
      ];

      tables.forEach(table => {
        if (table.guests.length > 0) {
          table.guests.forEach((guest, index) => {
            detailedData.push([
              table.name,
              index + 1,
              guest.title || '',
              guest.name || '',
              guest.surname || '',
              guest.email || '',
              guest.isLateGuest ? 'Late Guest' : 'Regular Guest'
            ]);
          });
        }
      });

      const detailedSheet = XLSX.utils.aoa_to_sheet(detailedData);
      XLSX.utils.book_append_sheet(workbook, detailedSheet, 'Detailed Seating');

      // Create individual table sheets for tables with guests
      tables.forEach(table => {
        if (table.guests.length > 0) {
          const tableData = [
            [`${table.name} - ${table.guests.length}/${maxSeatsPerTable} guests`],
            [''],
            ['Seat #', 'Title', 'Name', 'Surname', 'Email', 'Type']
          ];

          table.guests.forEach((guest, index) => {
            tableData.push([
              index + 1,
              guest.title || '',
              guest.name || '',
              guest.surname || '',
              guest.email || '',
              guest.isLateGuest ? 'Late Guest' : 'Regular Guest'
            ]);
          });

          const tableSheet = XLSX.utils.aoa_to_sheet(tableData);
          XLSX.utils.book_append_sheet(workbook, tableSheet, table.name);
        }
      });

      XLSX.writeFile(workbook, 'wedding-table-seating-arrangement.xlsx');
    };

    return (
      <div className="container table-seating-container">
        <h1>Table Seating Arrangement</h1>

        {/* Export buttons */}
        <div className="export-buttons-container" style={{ marginBottom: '20px', display: 'flex', gap: '10px' }}>
          <button onClick={exportToPDF} className="export-button pdf-button">
            📄 Download PDF
          </button>
          <button onClick={exportToExcel} className="export-button excel-button">
            📊 Download Excel
          </button>
        </div>

        <div className="seating-layout">
          <div className="tables-section">
            <h2>Tables ({tables.length})</h2>
            <div className="tables-grid">
              {tables.map(table => (
                <div 
                  key={table.id} 
                  className={`table-card ${selectedTable === table.id ? 'selected' : ''}`}
                  onClick={() => setSelectedTable(table.id)}
                >
                  <h3>{table.name}</h3>
                  <div className="seat-count">
                    {table.guests.length}/{maxSeatsPerTable} seats filled
                  </div>
                  {selectedTable === table.id && (
                    <div className="table-guests">
                      {table.guests.length > 0 ? (
                        <ul>
                          {table.guests.map(guest => {
                            const maskedGuest = maskGuestData(guest, user);
                            return (
                              <li key={guest.email}>
                                {maskedGuest.title} {maskedGuest.name} {maskedGuest.surname}
                                {guest.isLateGuest && (
                                  <span className="late-guest-badge" title="Late Guest">
                                    🕐
                                  </span>
                                )}
                                {user?.displayName?.toLowerCase() === 'onesmus' && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      removeGuestFromTable(table.id, getGuestId(guest));
                                    }}
                                    className="remove-guest-btn"
                                  >
                                    ×
                                  </button>
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      ) : (
                        <p className="empty-table">No guests assigned</p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div className="guests-section">
            <h2>Approved Guests (Including Late Guests)</h2>
            <div className="search-container">
              <input
                type="text"
                placeholder="Search by name or surname..."
                value={guestSearch}
                onChange={(e) => setGuestSearch(e.target.value)}
                className="guest-search"
              />
              {guestSearch && (
                <button
                  onClick={() => setGuestSearch('')}
                  className="clear-search-button"
                  title="Clear search"
                >
                  ✕
                </button>
              )}
            </div>
            <p style={{fontSize: '0.8rem', color: '#666'}}>
              {guestSearch ? (
                <>Searching for "{guestSearch}" | Showing {filteredAvailableGuests.length} of {availableGuests.length} guests</>
              ) : (
                <>Showing all {availableGuests.length} approved guests</>
              )}
            </p>
            
            <div className="available-guests" key={`search-${guestSearch}-${filteredAvailableGuests.length}`}>
              {filteredAvailableGuests.length > 0 ? (
                filteredAvailableGuests.map((guest) => {
                  const guestId = getGuestId(guest);
                  const maskedGuest = maskGuestData(guest, user);
                  // Check if guest is already assigned to a table using unique guest ID
                  const assignedTable = tables.find(table =>
                    table.guests.some(g => getGuestId(g) === guestId)
                  );

                  return (
                    <div key={guestId} className="guest-card">
                      <div className="guest-info">
                        <span className="guest-name">
                          {maskedGuest.title} {maskedGuest.name} {maskedGuest.surname}
                          {guest.isLateGuest && (
                            <span className="late-guest-badge" title="Late Guest">
                              🕐
                            </span>
                          )}
                        </span>
                        {assignedTable && (
                          <span className="assigned-table">
                            Assigned to: {assignedTable.name}
                          </span>
                        )}
                      </div>

                      {selectedTable && (
                        <button
                          onClick={() => assignGuestToTable(
                            guestId,
                            selectedTable
                          )}
                          disabled={assignedTable && assignedTable.id === selectedTable}
                          className="assign-button"
                        >
                          {assignedTable && assignedTable.id === selectedTable
                            ? 'Already at this table'
                            : assignedTable
                              ? 'Move to this table'
                              : 'Assign to table'}
                        </button>
                      )}
                    </div>
                  );
                })
              ) : (
                <p className="no-guests">No approved guests found</p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="app-container">
      <nav className="app-nav">
        <div className="app-logo">Wedding Dashboard</div>
        <ul className="nav-links">
          <li>
            <button 
              className={currentView === 'dashboard' ? 'active' : ''} 
              onClick={() => handleNavigation('dashboard')}
            >
              Dashboard
            </button>
          </li>
          <li>
            <button 
              className={currentView === 'giftRegistry' ? 'active' : ''} 
              onClick={() => handleNavigation('giftRegistry')}
            >
              Gift Registry
            </button>
          </li>
          {user.role === 'admin' && (
            <li>
              <button 
                className={currentView === 'createAdmin' ? 'active' : ''} 
                onClick={() => handleNavigation('createAdmin')}
              >
                Manage Admins
              </button>
            </li>
          )}
          <li>
            <button
              className={currentView === 'tableSeating' ? 'active' : ''}
              onClick={() => handleNavigation('tableSeating')}
            >
              Table Seating
            </button>
          </li>
          <li>
            <button
              className={currentView === 'emailList' ? 'active' : ''}
              onClick={() => handleNavigation('emailList')}
            >
              Email List
            </button>
          </li>
          <li>
            <button
              className={currentView === 'lateGuests' ? 'active' : ''}
              onClick={() => handleNavigation('lateGuests')}
            >
              Late Guests
            </button>
          </li>
        </ul>
        <div className="user-actions">
          <button className="logout-button" onClick={handleLogout}>Logout</button>
          <div className="user-info">
            <span>{user.displayName}</span>
          </div>
        </div>
      </nav>

      <main className="app-content">
        {currentView === 'dashboard' && (
          <div className="container">
            <h1>Wedding Guest Dashboard</h1>

            {/* Stats View Toggle Buttons */}
            <div className="view-toggle">
              <button 
                onClick={() => handleStatViewChange('behalfOf')} 
                className={`toggle-button ${statView === 'behalfOf' ? 'active' : ''}`}
              >
                View by Behalf Of
              </button>
              <button 
                onClick={() => handleStatViewChange('titles')} 
                className={`toggle-button ${statView === 'titles' ? 'active' : ''}`}
              >
                View by Titles (Mr/Mrs)
              </button>
              <button 
                onClick={() => handleStatViewChange('approval')} 
                className={`toggle-button ${statView === 'approval' ? 'active' : ''}`}
              >
                View by Approval Status
              </button>
            </div>

            {/* Stats & Graph Section */}
            {statView === 'behalfOf' && <BehalfOfStatsView />}
            {statView === 'titles' && <TitlesStatsView />}
            {statView === 'approval' && <ApprovalStatsView />}

            {/* Search & Filter */}
            <div className="filters">
              <input type="text" placeholder="Search guests..." value={searchTerm} onChange={handleSearch} />
              <select value={filterBy} onChange={handleFilterChange}>
                <option value="all">All Guests</option>
                <option value="Onesmus">Onesmus's Guests</option>
                <option value="Colleta">Colleta's Guests</option>
              </select>
              <select value={approvalFilter} onChange={handleApprovalFilterChange}>
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="declined">Declined</option>
              </select>
              <button onClick={exportToCSV}>Export CSV</button>
            </div>

            {/* Table */}
            <div className="table-container">
              <table>
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Name</th>
                    <th>Surname</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Behalf Of</th>
                    <th>Status</th>
                    <th>Password</th>
                    <th>Email Sent</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredGuests.map((guest, index) => {
                    const maskedGuest = maskGuestData(guest, user);
                    return (
                      <tr key={index} className={`status-${guest.approvalStatus}`}>
                        <td>{maskedGuest.title || "—"}</td>
                        <td>{maskedGuest.name}</td>
                        <td>{maskedGuest.surname}</td>
                        <td>{maskedGuest.email}</td>
                        <td>{maskedGuest.phone}</td>
                        <td>{guest.behalfOf}</td>
                        <td className={`status-cell ${guest.approvalStatus}`}>
                          {guest.approvalStatus.charAt(0).toUpperCase() + guest.approvalStatus.slice(1)}
                        </td>
                        <td>{guest.password ? "••••••••" : "—"}</td>
                      <td className="email-sent-cell">
                        {guest.approvalStatus === "approved" && (
                          emailSendingStatus[guest.email] === "sending" ? (
                            <span className="sending-email">Sending...</span>
                          ) : emailSendingStatus[guest.email] === "sent" || guest.emailSent ? (
                            <span className="email-status sent">✓ Sent</span>
                          ) : (
                            <span className="email-status not-sent">Not Sent</span>
                          )
                        )}
                      </td>
                      <td className="action-buttons">
                        {guest.approvalStatus !== "approved" && (
                          <button 
                            onClick={() => handleApprovalChange(index, "approved")} 
                            className="approve-button"
                            title="Approve"
                          >
                            ✓
                          </button>
                        )}
                        {guest.approvalStatus !== "declined" && (
                          <button 
                            onClick={() => handleApprovalChange(index, "declined")} 
                            className="decline-button"
                            title="Decline"
                          >
                            ✗
                          </button>
                        )}
                        <button
                          onClick={() => handleEditClick(index)}
                          className="edit-button"
                          title="Edit"
                        >
                          ✎
                        </button>
                        {user?.displayName?.toLowerCase() === 'onesmus' && (
                          <button
                            onClick={() => deleteGuest(index)}
                            className="delete-button"
                            title="Delete"
                          >
                            🗑️
                          </button>
                        )}
                      </td>
                    </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {currentView === 'giftRegistry' && <GiftRegistryView />}
        
        {currentView === 'createAdmin' && (
          <CreateAdmin currentUser={user} />
        )}
        
        {currentView === 'tableSeating' && <TableSeatingView />}

        {currentView === 'emailList' && <EmailListView />}
        {currentView === 'lateGuests' && <LateGuestsDashboard currentUser={user} />}
      </main>
      
      {/* Edit Form Modal */}
      {editMode && <EditForm />}
      
      {/* Password Modal */}
      {showPasswordModal && <PasswordModal />}
    </div>
  );
};

export default App;
